<template>
  <div>
    <el-row :gutter="32" class="panel-group">
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-people">
            <svg-icon icon-class="example" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">当日利润</div>
            <count-to :start-val="0" :end-val="dailyProfit" :duration="2000" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="edit" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">当日总额</div>
            <count-to :start-val="0" :end-val="transactionAmount" :duration="2500" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-money">
            <svg-icon icon-class="money" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总体利润</div>
            <count-to :start-val="0" :end-val="totalProfit" :duration="2800" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="number" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">当前结余利润</div>
            <count-to :start-val="0" :end-val="totalSurplusProfit" :duration="2600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="32" class="panel-group">
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="peoples" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">用户总数</div>
            <count-to :start-val="0" :end-val="peopleCount" :duration="3200" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="redis" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">总贡献值</div>
            <count-to :start-val="0" :end-val="integralGxzSum" :duration="2200" class="card-panel-num" />
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="tool" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">通用积分</div>
            <count-to :start-val="0" :end-val="integralTySum" :duration="2600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6" class="card-panel-col">
        <div class="card-panel">
          <div class="card-panel-icon-wrapper icon-shopping">
            <svg-icon icon-class="star" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">优惠券总值</div>
            <count-to :start-val="0" :end-val="consumeSum" :duration="2600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CountTo from "vue-count-to";
import { capital } from "@/api/seting/capital";
export default {
  components: {
    CountTo,
  },
  data() {
    return {
      timer: null,
      dailyProfit: 0,
      transactionAmount: 0,
      integralGxzSum: 0,
      peopleCount: 0,
      integralTySum: 0,
      totalProfit: 0,
      consumeSum: 0,
      totalSurplusProfit: 0,
    };
  },
  methods: {
    init() {
      this.getCapital();
    },
    getCapital() {
      capital().then((res) => {
        this.peopleCount = res.data.peopleCount || 0;
        this.integralGxzSum = res.data.integralGxzSum || 0;
        this.dailyProfit = res.data.dailyProfit || 0;
        this.transactionAmount = res.data.transactionAmount || 0;
        this.integralTySum = res.data.integralTySum || 0;
        this.totalProfit = res.data.totalProfit || 0;
        this.consumeSum = res.data.consumeSum || 0;
        this.totalSurplusProfit = res.data.totalSurplusProfit || 0;
      });
    },
  },
  created() {
    this.init();
    // 创建定时任务
    this.timer = setInterval(() => {
      this.init();
    }, 30000);
  },
  beforeDestroy() {
    // 清理定时任务
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 20px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      text-align: center;
      font-weight: bold;
      margin: 26px 0px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>
