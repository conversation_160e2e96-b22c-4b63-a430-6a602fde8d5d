<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择转账状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入关联订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="openid">
        <el-input
          v-model="queryParams.openid"
          placeholder="请输入收款方openid"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入收款方姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['wechat:payment:transfer:list']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="transferList" border>
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="关联订单号" align="center" prop="orderId" />
      <el-table-column label="批次号" align="center" prop="outBatchNo" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="收款方openid" align="center" prop="openid" />
      <el-table-column label="收款方姓名" align="center" prop="userName" />
      <el-table-column label="金额(分)" align="center" prop="amount" />
      <el-table-column label="转账备注" align="center" prop="transferRemark" />
      <el-table-column label="转账状态" align="center" prop="transferStatus" />
      <el-table-column label="是否成功" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="转账时间" align="center" prop="transferTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.transferTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['wechat:payment:transfer:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleReissue(scope.row)"
            v-hasPermi="['wechat:payment:transfer:edit']"
            v-if="scope.row.status == 0"
          >重新转账</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看微信转账信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="主键：">
          <span>{{ form.id }}</span>
        </el-form-item>
        <el-form-item label="关联订单号：">
          <span>{{ form.orderId }}</span>
        </el-form-item>
        <el-form-item label="批次号：">
          <span>{{ form.outBatchNo }}</span>
        </el-form-item>
        <el-form-item label="收款方openid：">
          <span>{{ form.openid }}</span>
        </el-form-item>
        <el-form-item label="收款方姓名：">
          <span>{{ form.userName }}</span>
        </el-form-item>
        <el-form-item label="金额(分)：">
          <span>{{ form.amount }}</span>
        </el-form-item>
        <el-form-item label="转账备注：">
          <span>{{ form.transferRemark }}</span>
        </el-form-item>
        <el-form-item label="转账状态：">
          <span>{{ form.transferStatus }}</span>
        </el-form-item>
        <el-form-item label="是否成功：">
          <dict-tag :options="dict.type.sys_yes_no" :value="form.status" />
        </el-form-item>
        <el-form-item label="创建时间：">
          <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
        <el-form-item label="转账时间：">
          <span>{{ parseTime(form.transferTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
        <el-form-item label="微信返回信息：">
          <el-input
            v-model="form.response"
            type="textarea"
            :rows="4"
            readonly
          />
        </el-form-item>
        <el-form-item label="失败原因：" v-if="form.failReason">
          <el-input
            v-model="form.failReason"
            type="textarea"
            :rows="2"
            readonly
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWeChatTransfer,
  getWeChatTransfer,
  reissueWeChatTransfer,
} from "@/api/wechat/transfer";

export default {
  name: "WeChatTransfer",
  dicts: ["sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 微信转账信息表格数据
      transferList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId: null,
        openid: null,
        userName: null,
        amount: null,
        transferRemark: null,
        status: null,
        createTime: null,
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询微信转账信息列表 */
    getList() {
      this.loading = true;
      listWeChatTransfer(this.queryParams).then((response) => {
        this.transferList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderId: null,
        outBatchNo: null,
        openid: null,
        userName: null,
        amount: null,
        transferRemark: null,
        transferStatus: null,
        status: null,
        response: null,
        failReason: null,
        transferTime: null,
        createTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getWeChatTransfer(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "查看微信转账信息";
      });
    },
    /** 重新转账按钮操作 */
    handleReissue(row) {
      this.$modal.confirm('是否确认重新发起转账"' + row.id + '"？').then(function() {
        return reissueWeChatTransfer(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("重新转账成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('wechat/payment/transfer/export', {
        ...this.queryParams
      }, `wechat_transfer_${new Date().getTime()}.xlsx`)
    }
  },
};
</script>
