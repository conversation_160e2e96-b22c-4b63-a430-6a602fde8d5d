<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="" prop="payStatus">
        <el-select
          v-model="queryParams.payStatus"
          placeholder="请选择支付状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入商品标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="buyerUserId">
        <el-input
          v-model="queryParams.buyerUserId"
          placeholder="请输入买家用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['wechat:payment:order:list']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" border>
      <el-table-column label="订单号" align="center" prop="id" />
      <el-table-column label="商品标题" align="center" prop="subject" />
      <el-table-column label="订单金额" align="center" prop="totalAmount" />
      <el-table-column label="实际支付金额" align="center" prop="actualTotalAmount" />
      <el-table-column label="买家用户ID" align="center" prop="buyerUserId" />
      <el-table-column label="支付状态" align="center" prop="payStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.payStatus" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="receiptDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.receiptDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['wechat:payment:order:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleQueryStatus(scope.row)"
            v-hasPermi="['wechat:payment:order:query']"
          >查询状态</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleClose(scope.row)"
            v-hasPermi="['wechat:payment:order:close']"
            v-if="scope.row.payStatus == 0"
          >关闭订单</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看微信支付信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="订单号：">
          <span>{{ form.id }}</span>
        </el-form-item>
        <el-form-item label="商品标题：">
          <span>{{ form.subject }}</span>
        </el-form-item>
        <el-form-item label="订单金额：">
          <span>{{ form.totalAmount }}</span>
        </el-form-item>
        <el-form-item label="实际支付金额：">
          <span>{{ form.actualTotalAmount }}</span>
        </el-form-item>
        <el-form-item label="买家用户ID：">
          <span>{{ form.buyerUserId }}</span>
        </el-form-item>
        <el-form-item label="支付状态：">
          <dict-tag :options="dict.type.sys_yes_no" :value="form.payStatus" />
        </el-form-item>
        <el-form-item label="创建时间：">
          <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
        <el-form-item label="支付时间：">
          <span>{{ parseTime(form.receiptDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
        <el-form-item label="微信返回信息：">
          <el-input
            v-model="form.response"
            type="textarea"
            :rows="4"
            readonly
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWeChatOrder,
  getWeChatOrder,
  queryWeChatOrderStatus,
  closeWeChatOrder,
} from "@/api/wechat/order";

export default {
  name: "WeChatOrder",
  dicts: ["sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 微信支付信息表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subject: null,
        totalAmount: null,
        actualTotalAmount: null,
        buyerUserId: null,
        payStatus: null,
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询微信支付信息列表 */
    getList() {
      this.loading = true;
      listWeChatOrder(this.queryParams).then((response) => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        subject: null,
        totalAmount: null,
        actualTotalAmount: null,
        buyerUserId: null,
        payStatus: null,
        response: null,
        receiptDate: null,
        createTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id;
      getWeChatOrder(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "查看微信支付信息";
      });
    },
    /** 查询订单状态 */
    handleQueryStatus(row) {
      this.$modal.confirm('是否确认查询订单"' + row.id + '"的状态？').then(function() {
        return queryWeChatOrderStatus(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("查询成功");
      }).catch(() => {});
    },
    /** 关闭订单 */
    handleClose(row) {
      this.$modal.confirm('是否确认关闭订单"' + row.id + '"？').then(function() {
        return closeWeChatOrder(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("关闭成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('wechat/payment/order/export', {
        ...this.queryParams
      }, `wechat_order_${new Date().getTime()}.xlsx`)
    }
  },
};
</script>
