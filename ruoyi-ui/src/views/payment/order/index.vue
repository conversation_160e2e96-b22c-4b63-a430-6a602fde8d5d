<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="订单主键" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入付款金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单名称" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入付款金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="交易状态" prop="payStatus">
        <el-select
          v-model="queryParams.payStatus"
          placeholder="请选择交易状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_commodity_order_pay_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="实际支付金额" prop="actualTotalAmount">
        <el-input
          v-model="queryParams.actualTotalAmount"
          placeholder="请输入实际支付金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" border>
      <el-table-column label="订单主键" align="center" prop="id" />
      <el-table-column
        label="订单名称"
        align="center"
        prop="subject"
        show-overflow-tooltip
      />
      <el-table-column label="付款金额" align="center" prop="totalAmount" />
      <el-table-column
        label="实际支付金额"
        align="center"
        prop="actualTotalAmount"
      />
      <el-table-column label="卖家主键" align="center" prop="sellerUserId" />
      <el-table-column label="买家主键" align="center" prop="buyerUserId" />
      <el-table-column label="交易状态" align="center" prop="payStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_commodity_order_pay_status"
            :value="scope.row.payStatus"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="支付成功回调时间"
        align="center"
        prop="notifyDate"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ali:trends:notify:offline']"
            v-if="scope.row.payStatus == 0"
            >手动回调</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listOrder, getOrder, offline } from "@/api/payment/order";

export default {
  name: "Order",
  dicts: ["sxsc_commodity_order_pay_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 支付宝支付信息表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: "",
        subject: null,
        totalAmount: null,
        actualTotalAmount: null,
        sellerUserId: null,
        buyerUserId: null,
        payStatus: null,
        response: null,
        notifyDate: null,
        notifyResponse: null,
        receiptDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询支付宝支付信息列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then((response) => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        subject: null,
        totalAmount: null,
        actualTotalAmount: null,
        sellerUserId: null,
        buyerUserId: null,
        payStatus: null,
        response: null,
        notifyDate: null,
        notifyResponse: null,
        receiptDate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      offline(row.batchId).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
  },
};
</script>
