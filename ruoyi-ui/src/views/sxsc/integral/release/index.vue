<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>积分释放比例配置</span>
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="当前释放比例" prop="releaseRatio">
          <el-input-number
            v-model="form.releaseRatio"
            :precision="4"
            :step="0.0001"
            :min="0"
            :max="1"
            placeholder="请输入释放比例"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399;">
            范围：0.0000 - 1.0000，0表示不释放
          </span>
        </el-form-item>
        
        <el-form-item label="配置说明">
          <div style="color: #606266; line-height: 1.6;">
            <p>• 积分释放比例支持小数点后四位精度</p>
            <p>• 例如：0.0100 表示每日释放1%的积分</p>
            <p>• 释放的积分将转换为等额佣金添加到用户账户</p>
            <p>• 设置为0.0000表示暂停积分释放</p>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            保存配置
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>操作记录</span>
      </div>
      
      <el-table v-loading="tableLoading" :data="operationList" border>
        <el-table-column label="操作时间" align="center" prop="updateTime" width="180" />
        <el-table-column label="操作人" align="center" prop="updateBy" width="120" />
        <el-table-column label="释放比例" align="center" prop="configValue" width="120" />
        <el-table-column label="备注" align="center" prop="remark" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { listConfig, getConfigKey, updateConfig } from "@/api/system/config";

export default {
  name: "IntegralRelease",
  data() {
    return {
      loading: false,
      tableLoading: false,
      form: {
        releaseRatio: 0.0000
      },
      rules: {
        releaseRatio: [
          { required: true, message: "释放比例不能为空", trigger: "blur" },
          { type: "number", min: 0, max: 1, message: "释放比例必须在0-1之间", trigger: "blur" }
        ]
      },
      operationList: [],
      configKey: "sxsc.integral.release.ratio",
      configInfo: null
    };
  },
  created() {
    this.getConfig();
  },
  methods: {
    /** 获取配置 */
    getConfig() {
      this.loading = true;

      // 先查询配置列表获取完整的配置信息
      listConfig({
        configKey: this.configKey
      }).then(response => {
        if (response.rows && response.rows.length > 0) {
          this.configInfo = response.rows[0];
          this.form.releaseRatio = parseFloat(this.configInfo.configValue) || 0.0000;
        } else {
          // 如果配置不存在，使用默认值
          this.form.releaseRatio = 0.0000;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;

          if (this.configInfo) {
            // 更新现有配置
            const updateData = {
              configId: this.configInfo.configId,
              configName: this.configInfo.configName,
              configKey: this.configKey,
              configValue: this.form.releaseRatio.toFixed(4),
              configType: this.configInfo.configType,
              remark: "积分每日释放比例，支持小数点后四位，范围0.0000-1.0000，0表示不释放"
            };

            updateConfig(updateData).then(response => {
              this.$modal.msgSuccess("配置保存成功");
              this.loading = false;
              this.getConfig();
            }).catch(() => {
              this.loading = false;
            });
          } else {
            this.$modal.msgError("配置信息不存在，请先执行SQL脚本初始化配置");
            this.loading = false;
          }
        }
      });
    },

    /** 重置表单 */
    resetForm() {
      this.getConfig();
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
