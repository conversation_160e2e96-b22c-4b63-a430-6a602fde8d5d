<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="申请日期" prop="createTime">
        <el-date-picker
          clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择申请日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" border key="id">
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="PT钱包" align="center" prop="ptAddress">
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="createTime" />
      <el-table-column label="提现金额" align="center" prop="amount" />
      <el-table-column label="审核是否通过" align="center" prop="examineStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_yes_no"
            :value="scope.row.examineStatus"
          />
        </template>
      </el-table-column>

      <el-table-column label="审核时间" align="center" prop="updateTime">
      </el-table-column>
      <el-table-column label="提现方式" align="center" prop="paymentType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_commission_payment_type"
            :value="scope.row.paymentType"
          />
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_commission_payment_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="到账时间" align="center" prop="paymentTime">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row, 1)"
            v-if="scope.row.status == 0"
            v-hasPermi="['commission:order:edit']"
            :loading="loading"
            >通过</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row, 0)"
            v-if="scope.row.status == 0"
            v-hasPermi="['commission:order:edit']"
            :loading="loading"
            >拒绝</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listOrder, updateOrder } from "@/api/person/commission/order";

export default {
  name: "Order",
  dicts: [
    "sys_yes_no",
    "sxsc_commission_payment_type",
    "sxsc_commission_payment_status",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 佣金提现表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        examineUserId: null,
        examineStatus: null,
        examineTime: null,
        status: null,
        paymentTime: null,
        paymentType: 4,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询佣金提现列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then((response) => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        amount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        examineUserId: null,
        examineStatus: null,
        examineTime: null,
        status: null,
        paymentTime: null,
        paymentType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row, examineStatus) {
      this.loading = true;
      const id = row.id || this.ids;
      updateOrder({ id: id, examineStatus: examineStatus }).then((response) => {
        this.$modal.msgSuccess("审核完成");
        this.getList();
        this.loading = false;
      });
    },
  },
};
</script>
