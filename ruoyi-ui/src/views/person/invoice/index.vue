<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
    >
      <el-form-item label="发票抬头" prop="invoiceHeader">
        <el-input
          v-model="queryParams.invoiceHeader"
          placeholder="请输入发票抬头"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位税号" prop="taxId">
        <el-input
          v-model="queryParams.taxId"
          placeholder="请输入单位税号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="注册地址" prop="registerAddress">
        <el-input
          v-model="queryParams.registerAddress"
          placeholder="请输入注册地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="注册电话" prop="registerPhone">
        <el-input
          v-model="queryParams.registerPhone"
          placeholder="请输入注册电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开户银行" prop="openingBank">
        <el-input
          v-model="queryParams.openingBank"
          placeholder="请输入开户银行"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="银行账号" prop="bankNumber">
        <el-input
          v-model="queryParams.bankNumber"
          placeholder="请输入银行账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="invoiceList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="用户昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="用户手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="发票抬头" align="center" prop="invoiceHeader" />
      <el-table-column label="单位税号" align="center" prop="taxId" />
      <el-table-column label="注册地址" align="center" prop="registerAddress" />
      <el-table-column label="注册电话" align="center" prop="registerPhone" />
      <el-table-column label="开户银行" align="center" prop="openingBank" />
      <el-table-column label="银行账号" align="center" prop="bankNumber" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['person:invoice:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['person:invoice:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改发票信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="发票抬头" prop="invoiceHeader">
          <el-input v-model="form.invoiceHeader" placeholder="请输入发票抬头" />
        </el-form-item>
        <el-form-item label="单位税号" prop="taxId">
          <el-input v-model="form.taxId" placeholder="请输入单位税号" />
        </el-form-item>
        <el-form-item label="注册地址" prop="registerAddress">
          <el-input
            v-model="form.registerAddress"
            placeholder="请输入注册地址"
          />
        </el-form-item>
        <el-form-item label="注册电话" prop="registerPhone">
          <el-input v-model="form.registerPhone" placeholder="请输入注册电话" />
        </el-form-item>
        <el-form-item label="开户银行" prop="openingBank">
          <el-input v-model="form.openingBank" placeholder="请输入开户银行" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankNumber">
          <el-input v-model="form.bankNumber" placeholder="请输入银行账号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInvoice,
  getInvoice,
  delInvoice,
  addInvoice,
  updateInvoice,
} from "@/api/person/invoice";

export default {
  name: "Invoice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 发票信息表格数据
      invoiceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        invoiceHeader: null,
        taxId: null,
        registerAddress: null,
        registerPhone: null,
        openingBank: null,
        bankNumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户主键不能为空", trigger: "blur" },
        ],
        invoiceHeader: [
          { required: true, message: "发票抬头不能为空", trigger: "blur" }
        ],
        taxId: [
          { required: true, message: "单位税号不能为空", trigger: "blur" }
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询发票信息列表 */
    getList() {
      this.loading = true;
      listInvoice(this.queryParams).then((response) => {
        this.invoiceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        invoiceHeader: null,
        taxId: null,
        registerAddress: null,
        registerPhone: null,
        openingBank: null,
        bankNumber: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加发票信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInvoice(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改发票信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInvoice(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInvoice(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除发票信息？')
        .then(function () {
          return delInvoice(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
