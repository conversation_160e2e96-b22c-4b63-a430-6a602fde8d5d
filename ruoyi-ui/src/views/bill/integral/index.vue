<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="订单主键" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入订单主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账单名称" prop="billName">
        <el-input
          v-model="queryParams.billName"
          placeholder="请输入账单名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账单类型" prop="billType">
        <el-select
          v-model="queryParams.billType"
          placeholder="请选择账单类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_bill_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="warning"
          size="mini"
          @click="openBatch = true"
          v-hasPermi="['sxsc:bill:integral:htBatch:recharge']"
          >积分操作</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="integralList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="昵称" align="center" prop="sysUser.nickName" />
      <el-table-column
        label="手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="订单主键" align="center" prop="orderId" />
      <el-table-column label="账单名称" align="center" prop="billName" />
      <el-table-column label="账单类型" align="center" prop="billType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_bill_type"
            :value="scope.row.billType"
          />
        </template>
      </el-table-column>
      <el-table-column label="积分" align="center" prop="integral" />
      <el-table-column label="余额" align="center" prop="balance" />
      <el-table-column label="操作时间" align="center" prop="createTime">
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改账单信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单主键" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单主键" />
        </el-form-item>
        <el-form-item label="账单名称" prop="billName">
          <el-input v-model="form.billName" placeholder="请输入账单名称" />
        </el-form-item>
        <el-form-item label="账单类型" prop="billType">
          <el-select v-model="form.billType" placeholder="请选择账单类型">
            <el-option
              v-for="dict in dict.type.sxsc_bill_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入账号主键" />
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="form.balance" placeholder="请输入余额" />
        </el-form-item>
        <el-form-item label="积分" prop="integral">
          <el-input v-model="form.integral" placeholder="请输入积分" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <div>
      <el-dialog
        :visible.sync="openBatch"
        width="500px"
        :close-on-click-modal="false"
        :destroy-on-close="true"
      >
        <el-radio-group v-model="batchIntegralType">
          <el-radio
            v-for="dict in dict.type.sxsc_bill_type"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}</el-radio
          >
        </el-radio-group>
        <el-input
          style="margin-top: 20px"
          v-model="batchText"
          type="textarea"
          :rows="5"
          placeholder="批量操作积分，文本格式如下：17609518419(手机号),100(积分);"
        ></el-input>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="batchLoading" type="primary" @click="batchClick"
            >确定</el-button
          >
          <el-button @click="openBatch = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listIntegral,
  getIntegral,
  delIntegral,
  addIntegral,
  updateIntegral,
  htRechargeBatch,
} from "@/api/bill/integral";

export default {
  name: "Integral",
  dicts: ["sxsc_bill_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 批量操作遮罩层
      batchLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 账单信息表格数据
      integralList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 批量操作弹出层
      openBatch: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId: null,
        billName: null,
        billType: null,
        userId: null,
        balance: null,
        integral: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      //批量积分操作类型
      batchIntegralType: null,
      //批量积分操作文本
      batchText: null,
      //批量积分操作名称
      batchTextBillName: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询账单信息列表 */
    getList() {
      this.loading = true;
      listIntegral(this.queryParams).then((response) => {
        this.integralList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderId: null,
        billName: null,
        billType: null,
        userId: null,
        balance: null,
        integral: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加账单信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getIntegral(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改账单信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateIntegral(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIntegral(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除账单信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delIntegral(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "bill/integral/export",
        {
          ...this.queryParams,
        },
        `integral_${new Date().getTime()}.xlsx`
      );
    },
    //批量积分操作
    batchClick() {
      this.batchLoading = true;

      htRechargeBatch(
        {
          text: this.batchText.replace(/\n/g, ""),
        },
        this.batchIntegralType
      )
        .then((res) => {
          this.$alert(
            "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
              res.msg +
              "</div>",
            "导入结果",
            { dangerouslyUseHTMLString: true }
          );
          this.getList();
          this.openBatch = false;
          this.batchLoading = false;
        })
        .catch((res) => {
          this.batchLoading = false;
        });
    },
  },
};
</script>
