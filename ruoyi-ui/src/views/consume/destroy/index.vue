<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="用户账号" prop="params.phonenumber">
        <el-input v-model="queryParams.params.phonenumber" placeholder="请输入用户账号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="订单主键" prop="orderId">
        <el-input v-model="queryParams.orderId" placeholder="请输入订单主键" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>

        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['consume:destroy:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="destroyList" @selection-change="handleSelectionChange" border>
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="优惠券卡号" align="center" prop="consumeNumber" />
      <el-table-column label="优惠券金额" align="center" prop="consumeAmount" />
      <el-table-column label="订单主键" align="center" prop="orderId" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改优惠券销毁对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户主键" />
        </el-form-item>
        <el-form-item label="优惠券名称" prop="consumeName">
          <el-input v-model="form.consumeName" placeholder="请输入优惠券名称" />
        </el-form-item>
        <el-form-item label="优惠券金额" prop="consumeAmount">
          <el-input v-model="form.consumeAmount" placeholder="请输入优惠券金额" />
        </el-form-item>
        <el-form-item label="订单主键" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单主键" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDestroy,
  getDestroy,
  delDestroy,
  addDestroy,
  updateDestroy,
} from "@/api/consume/destroy";

export default {
  name: "Destroy",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券销毁表格数据
      destroyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        orderId: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券销毁列表 */
    getList() {
      this.loading = true;
      listDestroy(this.queryParams).then((response) => {
        this.destroyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        orderId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优惠券销毁";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDestroy(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改优惠券销毁";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDestroy(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDestroy(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除优惠券销毁编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDestroy(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "consume/destroy/export",
        {
          ...this.queryParams,
        },
        `destroy_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
