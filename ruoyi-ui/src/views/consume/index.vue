<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="优惠券卡号" prop="consumeNumber">
        <el-input
          v-model="queryParams.consumeNumber"
          placeholder="请输入优惠券卡号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="优惠券额" prop="consumeAmount">
        <el-input
          v-model="queryParams.consumeAmount"
          placeholder="请输入优惠券额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_consume_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="consumeList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="优惠券名称" align="center" prop="consumeName" />
      <el-table-column label="优惠券额" align="center" prop="consumeAmount" />
      <el-table-column
        label="优惠券卡号"
        align="center"
        prop="consumeNumber"
        min-width="110px"
      />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_consume_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['consume:remove']"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleGive(scope.row)"
            v-hasPermi="['consume:give:add']"
            >赠送</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改系统赠送优惠券对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form
        ref="formGive"
        :model="formGive"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="受赠用户手机号" prop="giveUsername">
          <el-input
            v-model="formGive.giveUsername"
            placeholder="请输入用户手机号"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listConsume, getConsume, delConsume } from "@/api/consume/consume";
import { addGive } from "@/api/consume/give";
export default {
  name: "Consume",
  dicts: ["sxsc_consume_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券表格数据
      consumeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        consumeNumber: null,
        status: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单参数
      formGive: {},
      // 表单校验
      rules: {
        giveUsername: [
          { required: true, message: "请输入用户手机号", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券列表 */
    getList() {
      this.loading = true;
      listConsume(this.queryParams).then((response) => {
        this.consumeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        consumeName: null,
        consumeAmount: null,
        consumeNumber: null,
        phonenumber: null,
        numberCount: null,
        status: null,
      };
      this.resetForm("form");
    },
    // 表单重置
    reset() {
      this.formGive = {
        userId: null,
        consumeNumber: null,
        giveUsername: null,
      };
      this.resetForm("formGive");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.consumeNumber);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.consumeNumber || this.ids;
      this.$modal
        .confirm('是否确认删除优惠券编号为"' + ids + '"的数据项？')
        .then(function () {
          return delConsume(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 赠送按钮操作 */
    handleGive(row) {
      this.reset();
      this.formGive.consumeNumber = row.consumeNumber;
      this.open = true;
      this.title = "赠送优惠券给指定用户";
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["formGive"].validate((valid) => {
        if (valid) {
          addGive(this.formGive).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
  },
};
</script>
