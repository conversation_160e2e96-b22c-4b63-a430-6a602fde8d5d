<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="编号" prop="id">
        <el-input v-model="queryParams.id" placeholder="请输入编号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布人手机号" prop="params.phonenumber">
        <el-input v-model="queryParams.params.phonenumber" placeholder="请输入发布人手机号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="购买人手机号" prop="params.buyPhonenumber">
        <el-input v-model="queryParams.params.buyPhonenumber" placeholder="请输入购买人手机号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['consume:purchase:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purchaseList" @selection-change="handleSelectionChange" border>
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="支付类型" align="center" prop="">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.commodity_pay_type" :value="scope.row.payType" />
        </template>
      </el-table-column>
      <el-table-column label="是否支付" align="center" prop="">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.payStatus" />
        </template>
      </el-table-column>
      <el-table-column label="优惠券预购数量" align="center" prop="purchaseNum" />
      <el-table-column label="优惠券预购面值" align="center" prop="purchaseAmount" />
      <el-table-column label="发布人昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="发布人手机号" align="center" prop="sysUser.userName" />
      <el-table-column label="购买人昵称" align="center" prop="buySysUser.nickName" />
      <el-table-column label="购买人手机号" align="center" prop="buySysUser.userName" />
      <el-table-column label="购买人扣除积分" align="center" prop="deductIntegrate" />
      <el-table-column label="购买人扣除集团股" align="center" prop="deductShare" />
      <el-table-column label="发布时间" align="center" prop="createTime" />
      <el-table-column label="购买时间" align="center" prop="updateTime" />
      <el-table-column label="是否删除" align="center" prop="">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.delFlag" />
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改优惠券预购订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="优惠券预购数量" prop="purchaseNum">
          <el-input v-model="form.purchaseNum" placeholder="请输入优惠券预购数量" />
        </el-form-item>
        <el-form-item label="是否删除1删除0未删除" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入是否删除1删除0未删除" />
        </el-form-item>
        <el-form-item label="优惠券预购面值" prop="purchaseAmount">
          <el-input v-model="form.purchaseAmount" placeholder="请输入优惠券预购面值" />
        </el-form-item>
        <el-form-item label="发布人" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入发布人" />
        </el-form-item>
        <el-form-item label="卖出人" prop="buyUserId">
          <el-input v-model="form.buyUserId" placeholder="请输入卖出人" />
        </el-form-item>
        <el-form-item label="卖出人扣除积分" prop="deductIntegrate">
          <el-input v-model="form.deductIntegrate" placeholder="请输入卖出人扣除积分" />
        </el-form-item>
        <el-form-item label="卖出人扣除集团股" prop="deductShare">
          <el-input v-model="form.deductShare" placeholder="请输入卖出人扣除集团股" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurchase, getPurchase, delPurchase, addPurchase, updatePurchase } from "@/api/consume/purchase";

export default {
  name: "Purchase",
  dicts: ["sys_yes_no", "commodity_pay_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券预购订单表格数据
      purchaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseNum: null,
        purchaseAmount: null,
        userId: null,
        buyUserId: null,
        deductIntegrate: null,
        deductShare: null,
        params: { phonenumber: "", buyPhonenumbe: "" }
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券预购订单列表 */
    getList() {
      this.loading = true;
      listPurchase(this.queryParams).then(response => {
        this.purchaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        purchaseNum: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        purchaseAmount: null,
        userId: null,
        buyUserId: null,
        deductIntegrate: null,
        deductShare: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优惠券预购订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPurchase(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改优惠券预购订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePurchase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurchase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除优惠券预购订单编号为"' + ids + '"的数据项？').then(function () {
        return delPurchase(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('consume/purchase/export', {
        ...this.queryParams
      }, `purchase_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
