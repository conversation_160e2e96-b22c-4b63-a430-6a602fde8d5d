<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input v-model="queryParams.params.phonenumber" placeholder="请输入用户手机号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="优惠券卡号" prop="consumeNumber">
        <el-input v-model="queryParams.consumeNumber" placeholder="请输入优惠券卡号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="合并后的金额" prop="mergeConsumeAmount">
        <el-input v-model="queryParams.mergeConsumeAmount" placeholder="请输入合并后的金额" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mergeList" @selection-change="handleSelectionChange" border>
      <el-table-column label="用户昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="用户手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="优惠券卡号" align="center" prop="consumeNumber" />
      <el-table-column label="合并后的卡号" align="center" prop="mergeConsumeNumber" />
      <el-table-column label="合并后的金额" align="center" prop="mergeConsumeAmount" />
      <el-table-column label="消耗百分比" align="center" prop="consume" />
      <el-table-column label="操作时间" align="center" prop="createTime" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改优惠券合并对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户主键" />
        </el-form-item>
        <el-form-item label="优惠券卡号" prop="consumeNumber">
          <el-input v-model="form.consumeNumber" placeholder="请输入优惠券卡号" />
        </el-form-item>
        <el-form-item label="合并后的卡号" prop="mergeConsumeNumber">
          <el-input v-model="form.mergeConsumeNumber" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="合并后的金额" prop="mergeConsumeAmount">
          <el-input v-model="form.mergeConsumeAmount" placeholder="请输入合并后的金额" />
        </el-form-item>
        <el-form-item label="消耗百分比" prop="consume">
          <el-input v-model="form.consume" placeholder="请输入消耗百分比" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMerge,
  getMerge,
  delMerge,
  addMerge,
  updateMerge,
} from "@/api/consume/merge";

export default {
  name: "Merge",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券合并表格数据
      mergeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeNumber: null,
        mergeConsumeNumber: null,
        mergeConsumeAmount: null,
        consume: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        consumeNumber: [
          { required: true, message: "优惠券卡号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券合并列表 */
    getList() {
      this.loading = true;
      listMerge(this.queryParams).then((response) => {
        this.mergeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        consumeNumber: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        mergeConsumeNumber: null,
        mergeConsumeAmount: null,
        consume: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优惠券合并";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMerge(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改优惠券合并";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMerge(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMerge(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除优惠券合并编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMerge(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
  },
};
</script>
