<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input v-model="queryParams.params.phonenumber" placeholder="请输入用户手机号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="优惠券名称" prop="consumeName">
        <el-input v-model="queryParams.consumeName" placeholder="请输入优惠券名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="优惠券金额" prop="consumeAmount">
        <el-input v-model="queryParams.consumeAmount" placeholder="请输入优惠券金额" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="backList" @selection-change="handleSelectionChange" border>
      <el-table-column label="用户昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="用户手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="优惠券名称" align="center" prop="consumeName" />
      <el-table-column label="优惠券金额" align="center" prop="consumeAmount" />
      <el-table-column label="回购总数量" align="center" prop="number" />
      <el-table-column label="回购总金额" align="center" prop="buyBack" />
      <el-table-column label="回购时间" align="center" prop="createTime" />
      <el-table-column label="状态" align="center" prop="status">
        <template scope="scope">{{
          scope.row.status == 0 ? "待审核" : scope.row.status == 1 ? "通过" : "拒绝"
        }}</template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="updateBy" />
      <el-table-column label="审核时间" align="center" prop="updateTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleInfo(scope.row)">查看</el-button>
          <el-button size="mini" type="text" v-if="scope.row.status == 0"
            @click="handleUpdate(scope.row, 1)">通过</el-button>
          <el-button size="mini" type="text" v-if="scope.row.status == 0"
            @click="handleUpdate(scope.row, 2)">拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改优惠券回购信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-table :data="sxscUserConsumeBackDetailsList" border>
        <el-table-column label="序号" align="center" type="index" />
        <el-table-column label="优惠券名称" align="center" prop="consumeName" />
        <el-table-column label="优惠券金额" align="center" prop="consumeAmount" />
        <el-table-column label="回购金额" align="center" prop="buyBack" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBack,
  getBack,
  delBack,
  addBack,
  updateBack,
} from "@/api/consume/back";

export default {
  name: "Back",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedSxscUserConsumeBackDetails: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券回购信息表格数据
      backList: [],
      // 优惠券回购明细信息表格数据
      sxscUserConsumeBackDetailsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        number: null,
        buyBack: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券回购信息列表 */
    getList() {
      this.loading = true;
      listBack(this.queryParams).then((response) => {
        this.backList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        number: null,
        buyBack: null,
      };
      this.sxscUserConsumeBackDetailsList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优惠券回购信息";
    },
    /** 查看按钮操作 */
    handleInfo(row) {
      const id = row.id || this.ids;
      getBack(id).then((response) => {
        this.form = response.data;
        this.sxscUserConsumeBackDetailsList =
          response.data.sxscUserConsumeBackDetailsList;
        this.open = true;
        this.title = "优惠券回购信息";
      });
    },
    /** 审核按钮 */
    handleUpdate(row, status) {
      updateBack({ id: row.id, status: status }).then((response) => {
        this.$modal.msgSuccess("审核完毕");
        this.open = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.sxscUserConsumeBackDetailsList =
            this.sxscUserConsumeBackDetailsList;
          if (this.form.id != null) {
            updateBack(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBack(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除优惠券回购信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delBack(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
  },
};
</script>
