<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input v-model="queryParams.params.phonenumber" placeholder="请输入用户手机号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="订单主键" prop="commodityOrderId">
        <el-input v-model="queryParams.commodityOrderId" placeholder="请输入订单主键" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="优惠券卡号" prop="consumeNumber">
        <el-input v-model="queryParams.consumeNumber" placeholder="请输入优惠券卡号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['consume:buy:add']">新增</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['consume:buy:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="buyList" @selection-change="handleSelectionChange" border>
      <el-table-column label="用户昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="用户手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="订单主键" align="center" prop="commodityOrderId" />
      <el-table-column label="优惠券卡号" align="center" prop="consumeNumber" />
      <el-table-column label="抵押开始时间" align="center" prop="buyStartTime">
      </el-table-column>
      <el-table-column label="抵押结束时间" align="center" prop="buyEndTime">
      </el-table-column>
      <el-table-column label="优惠券金额" align="center" prop="consumeAmount" />
      <el-table-column label="抵押状态" align="center" prop="status">
        <template scope="scope">{{
          scope.row.status == 1 ? "抵押中" : "释放"
        }}</template>
      </el-table-column>
      <el-table-column label="抵押天数" align="center" prop="mortgageDays" />
      <el-table-column label="积分比例" align="center" prop="proportion" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['consume:buy:edit']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改优惠券抵押明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="30%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="抵押开始时间" prop="buyStartTime">
          <el-date-picker clearable v-model="form.buyStartTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择抵押开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="抵押结束时间" prop="buyEndTime">
          <el-date-picker clearable v-model="form.buyEndTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择抵押结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="抵押天数" prop="mortgageDays">
          <el-input-number v-model="form.mortgageDays" placeholder="请输入抵押天数" />
        </el-form-item>
        <el-form-item label="积分比例" prop="proportion">
          <el-input-number v-model="form.proportion" placeholder="请输入积分比例" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBuy, getBuy, updateBuy } from "@/api/consume/buy";

export default {
  name: "Buy",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券抵押明细表格数据
      buyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        commodityOrderId: null,
        consumeNumber: null,
        buyStartTime: null,
        buyEndTime: null,
        consumeAmount: null,
        status: null,
        mortgageDays: null,
        proportion: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户主键不能为空", trigger: "blur" },
        ],
        consumeNumber: [
          { required: true, message: "优惠券卡号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券抵押明细列表 */
    getList() {
      this.loading = true;
      listBuy(this.queryParams).then((response) => {
        this.buyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        commodityOrderId: null,
        consumeNumber: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        buyStartTime: null,
        buyEndTime: null,
        consumeAmount: null,
        status: null,
        mortgageDays: null,
        proportion: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优惠券抵押明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBuy(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改优惠券抵押明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBuy(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBuy(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除优惠券抵押明细编号为"' + ids + '"的数据项？')
        .then(function () {
          return delBuy(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "consume/buy/export",
        {
          ...this.queryParams,
        },
        `优惠券抵押明细_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
