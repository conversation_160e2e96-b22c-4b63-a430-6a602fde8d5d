<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="用户账号" prop="params.phonenumber">
        <el-input v-model="queryParams.params.phonenumber" placeholder="请输入用户账号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="优惠券面值" prop="consumeAmount">
        <el-input v-model="queryParams.consumeAmount" placeholder="请输入优惠券面值" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="优惠券卡号" prop="consumeNumber">
        <el-input v-model="queryParams.consumeNumber" placeholder="请输入抵押优惠券卡号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['consume:parentTeward:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="parentTewardList" @selection-change="handleSelectionChange" border>
      <el-table-column label="抵押用户主键" align="center" prop="userId" />
      <el-table-column label="抵押用户昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="抵押用户手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="优惠券面值" align="center" prop="consumeAmount" />
      <el-table-column label="抵押优惠券卡号" align="center" prop="consumeNumber" />
      <el-table-column label="获得面值比例积分" align="center" prop="proportion" />

      <el-table-column label="抵押用户父级主键" align="center" prop="parentUserId" />
      <el-table-column label="抵押用户推荐人昵称" align="center" prop="parentSysUser.nickName" />
      <el-table-column label="抵押用户推荐人手机号" align="center" prop="parentSysUser.phonenumber" />
      <el-table-column label="抵押时间" align="center" prop="pledgeTime">
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改优惠券抵押父级获得奖励明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="优惠券面值" prop="consumeAmount">
          <el-input v-model="form.consumeAmount" placeholder="请输入优惠券面值" />
        </el-form-item>
        <el-form-item label="抵押优惠券卡号" prop="consumeNumber">
          <el-input v-model="form.consumeNumber" placeholder="请输入抵押优惠券卡号" />
        </el-form-item>
        <el-form-item label="获得面值比例积分" prop="proportion">
          <el-input v-model="form.proportion" placeholder="请输入获得面值比例积分" />
        </el-form-item>
        <el-form-item label="抵押用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入抵押用户主键" />
        </el-form-item>
        <el-form-item label="抵押用户父级主键" prop="parentUserId">
          <el-input v-model="form.parentUserId" placeholder="请输入抵押用户父级主键" />
        </el-form-item>
        <el-form-item label="抵押时间" prop="pledgeTime">
          <el-date-picker clearable v-model="form.pledgeTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择抵押时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="释放比例" prop="release">
          <el-input v-model="form.release" placeholder="请输入释放比例" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listParentTeward,
  getParentTeward,
  delParentTeward,
  addParentTeward,
  updateParentTeward,
} from "@/api/consume/parentTeward";

export default {
  name: "ParentTeward",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优惠券抵押父级获得奖励明细表格数据
      parentTewardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        consumeAmount: null,
        consumeNumber: null,
        proportion: null,
        userId: null,
        parentUserId: null,
        pledgeTime: null,
        release: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询优惠券抵押父级获得奖励明细列表 */
    getList() {
      this.loading = true;
      listParentTeward(this.queryParams).then((response) => {
        this.parentTewardList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        consumeAmount: null,
        consumeNumber: null,
        proportion: null,
        userId: null,
        parentUserId: null,
        pledgeTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        release: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优惠券抵押父级获得奖励明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getParentTeward(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改优惠券抵押父级获得奖励明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateParentTeward(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addParentTeward(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除优惠券抵押父级获得奖励明细编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delParentTeward(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "consume/parentTeward/export",
        {
          ...this.queryParams,
        },
        `parentTeward_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
