<template>
  <div class="dashboard-editor-container">
    <div style="text-align: center">
      <span style="font-size: 36px">共有利润</span>
    </div>
    <div style="text-align: right">
      <span style="font-size: 18px">
        {{ parseTime(dateTime) }}
      </span>
    </div>
    <panel-group />
    <div style="text-align: center; font-size: 35px; margin: 20px; margin-top: 0px">
      优惠券值
    </div>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="6" v-for="(item, index) in consumData" :key="index">
        <div class="chart-wrapper">
          <div class="card-panel-title">{{ item.consumeName }}</div>
          <div class="card-panel-text" style="color: #40c9c6">
            可流通：{{ item.negotiable }}张
          </div>
          <div class="card-panel-text" style="color: #f4516c">
            冻结中：{{ item.freeze }}张
          </div>
          <div class="card-panel-text" style="color: #40a9a6">
            一卡值：{{ item.currentOneAmount }}元
          </div>
          <div class="card-panel-text" style="color: #10a1c1">
            回购价：{{ item.currentAmount }}元
          </div>
        </div>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import PanelGroup from "./dashboard/PanelGroup";
import { statisticsConsume } from "@/api/consume/consume";
export default {
  name: "Index",
  components: {
    PanelGroup
  },
  data() {
    return {
      consumData: [],
      dateTime: new Date(),
      timer: null,
      timerTime: null,
    };
  },
  methods: {
    getList() {
      statisticsConsume().then((res) => {
        this.consumData = res.data;
      });
    },
  },
  created() {
    this.getList();
    // 创建定时任务
    this.timer = setInterval(() => {
      this.getList();
    }, 30000);
    this.timerTime = setInterval(() => {
      this.dateTime = new Date();
    }, 1000);
  },
  beforeDestroy() {
    // 清理定时任务
    clearInterval(this.timer);
    // 清理定时任务
    clearInterval(this.timerTime);
  },
};
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 20px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px;
    margin-bottom: 32px;
  }
}

.card-panel-title {
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 22px;
  margin-bottom: 22px;
  text-align: center;
}

.card-panel-text {
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 22px;
  margin-bottom: 22px;
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.container {
  display: flex;
  align-items: center;
  /* 如果需要垂直居中 */
}

.container::before {
  content: "";
  flex-grow: 1;
  /* 占据剩余空间，将第一个 span 推到中间 */
}

.container span.push-right {
  margin-left: auto;
  /* 将第二个 span 推到右侧 */
}
</style>
