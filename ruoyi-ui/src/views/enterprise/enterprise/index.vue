<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="商家账号" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入商家账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="法人姓名" prop="legalPersonName">
        <el-input
          v-model="queryParams.legalPersonName"
          placeholder="请输入法人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="法人手机号" prop="legalPersonPhone">
        <el-input
          v-model="queryParams.legalPersonPhone"
          placeholder="请输入法人手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择审核状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.enterprise_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="企业类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择企业类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.enterprise_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="enterpriseList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="企业名称" align="center" prop="name" />
      <el-table-column label="营业执照" align="center" prop="license">
        <template slot-scope="scope">
          <image-preview :src="scope.row.license" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column
        label="对公账户"
        align="center"
        prop="corporateAccountBank"
      />
      <el-table-column
        label="对公账号"
        align="center"
        prop="corporateAccount"
      />
      <el-table-column label="法人姓名" align="center" prop="legalPersonName" />
      <el-table-column
        label="法人手机号"
        align="center"
        prop="legalPersonPhone"
      />
      <el-table-column label="审核状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.enterprise_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="企业类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.enterprise_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column label="省" align="center" prop="province" />
      <el-table-column label="市" align="center" prop="city" />
      <el-table-column label="县" align="center" prop="county" />
      <el-table-column label="自营" align="center" prop="self">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.self"
            :active-value="1"
            :inactive-value="0"
            @change="handleSelfChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="详细地址" align="center" prop="address" />
      <el-table-column label="申请时间" align="center" prop="createTime" />
      <el-table-column label="申请账号" align="center" prop="createBy" />
      <el-table-column label="操作" align="center" min-width="80px">
        <template slot-scope="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleExamine(scope.row)"
            v-if="scope.row.status == 0"
            v-hasPermi="['enterprise:examine:add']"
            >审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form
        ref="formExamine"
        :model="formExamine"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="审核描述" prop="remarks">
          <el-input
            v-model="formExamine.remarks"
            placeholder="请输入审核描述"
          />
        </el-form-item>
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="formExamine.status">
            <span v-for="dict in dict.type.enterprise_status" :key="dict.value">
              <el-radio v-if="dict.value != 0" :label="parseInt(dict.value)">{{
                dict.label
              }}</el-radio>
            </span>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEnterprise,
  getEnterprise,
  delEnterprise,
  addEnterprise,
  updateEnterprise,
  updateEnterpriseSelf,
} from "@/api/enterprise/enterprise";
import { addExamine } from "@/api/enterprise/examine";

export default {
  name: "Enterprise",
  dicts: ["enterprise_status", "enterprise_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业信息表格数据
      enterpriseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        name: null,
        license: null,
        corporateAccountBank: null,
        corporateAccount: null,
        legalPersonName: null,
        legalPersonPhone: null,
        proportion: null,
        status: null,
        type: null,
        longitude: null,
        latitude: null,
        annualFee: null,
        self: null,
        province: null,
        provinceCode: null,
        city: null,
        cityCode: null,
        county: null,
        countyCode: null,
        address: null,
      },
      // 表单参数
      form: {},
      formExamine: {},
      // 表单校验
      rules: {
        remarks: [
          { required: true, message: "审核描述不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "审核描述不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询企业信息列表 */
    getList() {
      this.loading = true;
      listEnterprise(this.queryParams).then((response) => {
        this.enterpriseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        name: null,
        license: null,
        corporateAccountBank: null,
        corporateAccount: null,
        legalPersonName: null,
        legalPersonPhone: null,
        proportion: null,
        status: null,
        type: null,
        longitude: null,
        latitude: null,
        annualFee: null,
        self: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        province: null,
        provinceCode: null,
        city: null,
        cityCode: null,
        county: null,
        countyCode: null,
        address: null,
      };
      this.resetForm("form");
    },
    // 表单重置
    resetExamine() {
      this.formExamine = {
        id: null,
        enterpriseId: null,
        remarks: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("formExamine");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业信息";
    },
    // 企业自营修改
    handleSelfChange(row) {
      let text = row.self === 0 ? "取消自营" : "指定直营";
      this.$modal
        .confirm("确认要" + text + "" + "用户吗？")
        .then(function () {
          return updateEnterpriseSelf({
            id: row.id,
            self: row.self,
          });
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.self = row.self === 0 ? 1 : 0;
        });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEnterprise(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEnterprise(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEnterprise(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 审核商家信息按钮操作 */
    handleExamine(row) {
      this.resetExamine();
      this.title = "审核商家信息";
      this.formExamine.enterpriseId = row.id;
      this.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["formExamine"].validate((valid) => {
        if (valid) {
          addExamine(this.formExamine).then((response) => {
            this.$modal.msgSuccess("审核成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
  },
};
</script>
