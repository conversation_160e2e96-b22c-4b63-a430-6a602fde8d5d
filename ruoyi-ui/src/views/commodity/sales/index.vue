<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="订单主键" prop="commodityOrderId">
        <el-input
          v-model="queryParams.commodityOrderId"
          placeholder="请输入订单主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="退款类型" prop="refundType">
        <el-select
          v-model="queryParams.refundType"
          placeholder="请选择退款类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.commodity_refund_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退货地址信息" prop="addressId">
        <el-input
          v-model="queryParams.addressId"
          placeholder="请输入退货地址信息"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="售后状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择售后状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.commodity_refund_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="salesList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="订单主键"
        align="center"
        prop="commodityOrderId"
      />
      <el-table-column label="申请时间" align="center" prop="createTime" />
      <el-table-column label="退款图片" align="center" prop="refundImg">
        <template slot-scope="scope">
          <image-preview :src="scope.row.refundImg" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="退款原因" align="center" prop="refundReason" />
      <el-table-column label="退款类型" align="center" prop="refundType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_refund_type"
            :value="scope.row.refundType"
          />
        </template>
      </el-table-column>
      <el-table-column label="拒绝退货原因" align="center" prop="refuseGoods" />
      <el-table-column
        label="拒绝退款原因"
        align="center"
        prop="refuseAmount"
      />
      <el-table-column label="售后状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_refund_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改订单售后信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="退款物流主键" prop="deliveryId">
          <el-input
            v-model="form.deliveryId"
            placeholder="请输入退款物流主键"
          />
        </el-form-item>
        <el-form-item label="退款图片" prop="refundImg">
          <image-upload v-model="form.refundImg" />
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input v-model="form.refundReason" placeholder="请输入退款原因" />
        </el-form-item>
        <el-form-item label="退款类型" prop="refundType">
          <el-select v-model="form.refundType" placeholder="请选择退款类型">
            <el-option
              v-for="dict in dict.type.commodity_refund_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退货地址信息" prop="addressId">
          <el-input v-model="form.addressId" placeholder="请输入退货地址信息" />
        </el-form-item>
        <el-form-item label="拒绝退货原因" prop="refuseGoods">
          <el-input
            v-model="form.refuseGoods"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="拒绝退款原因" prop="refuseAmount">
          <el-input
            v-model="form.refuseAmount"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="售后状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择售后状态">
            <el-option
              v-for="dict in dict.type.commodity_refund_status"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSales,
  getSales,
  delSales,
  addSales,
  updateSales,
} from "@/api/commodity/sales";

export default {
  name: "Sales",
  dicts: ["commodity_refund_status", "commodity_refund_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单售后信息表格数据
      salesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deliveryId: null,
        refundImg: null,
        refundReason: null,
        refundType: null,
        addressId: null,
        refuseGoods: null,
        refuseAmount: null,
        status: null,
        commodityOrderId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单售后信息列表 */
    getList() {
      this.loading = true;
      listSales(this.queryParams).then((response) => {
        this.salesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        commodityOrderId: null,
        deliveryId: null,
        refundImg: null,
        refundReason: null,
        refundType: null,
        addressId: null,
        refuseGoods: null,
        refuseAmount: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.commodityOrderId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加订单售后信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const commodityOrderId = row.commodityOrderId || this.ids;
      getSales(commodityOrderId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改订单售后信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.commodityOrderId != null) {
            updateSales(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSales(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const commodityOrderIds = row.commodityOrderId || this.ids;
      this.$modal
        .confirm(
          '是否确认删除订单售后信息编号为"' + commodityOrderIds + '"的数据项？'
        )
        .then(function () {
          return delSales(commodityOrderIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "commodity/sales/export",
        {
          ...this.queryParams,
        },
        `sales_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
