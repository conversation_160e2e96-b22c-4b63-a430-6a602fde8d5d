<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户主键" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联商品订单主键" prop="commodityOrderId">
        <el-input
          v-model="queryParams.commodityOrderId"
          placeholder="请输入关联商品订单主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发票状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择发票状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.commodity_order_invoice_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单完成时间" prop="orderCompletionTime">
        <el-date-picker
          clearable
          v-model="queryParams.orderCompletionTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择订单完成时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="开票完成时间" prop="invoicingTime">
        <el-date-picker
          clearable
          v-model="queryParams.invoicingTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择开票完成时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['commodity:invoice:add']"
          >新增</el-button
        >
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['commodity:invoice:remove']"
          >删除</el-button
        >
        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['commodity:invoice:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="invoiceList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="用户主键" align="center" prop="userId" />
      <el-table-column
        label="关联发票信息主键"
        align="center"
        prop="invoiceId"
      />
      <el-table-column
        label="关联商品订单主键"
        align="center"
        prop="commodityOrderId"
      />
      <el-table-column label="发票状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_order_invoice_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="发票图片地址" align="center" prop="imgUrl" />
      <el-table-column
        label="订单完成时间"
        align="center"
        prop="orderCompletionTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.orderCompletionTime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="开票完成时间"
        align="center"
        prop="invoicingTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.invoicingTime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['commodity:invoice:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['commodity:invoice:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商品订单发票信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户主键" />
        </el-form-item>
        <el-form-item label="关联发票信息主键" prop="invoiceId">
          <el-input
            v-model="form.invoiceId"
            placeholder="请输入关联发票信息主键"
          />
        </el-form-item>
        <el-form-item label="关联商品订单主键" prop="commodityOrderId">
          <el-input
            v-model="form.commodityOrderId"
            placeholder="请输入关联商品订单主键"
          />
        </el-form-item>
        <el-form-item label="发票状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.commodity_order_invoice_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票图片地址" prop="imgUrl">
          <el-input
            v-model="form.imgUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="订单完成时间" prop="orderCompletionTime">
          <el-date-picker
            clearable
            v-model="form.orderCompletionTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择订单完成时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开票完成时间" prop="invoicingTime">
          <el-date-picker
            clearable
            v-model="form.invoicingTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开票完成时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInvoice,
  getInvoice,
  delInvoice,
  addInvoice,
  updateInvoice,
} from "@/api/commodity/invoice";

export default {
  name: "Invoice",
  dicts: ["commodity_order_invoice_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品订单发票信息表格数据
      invoiceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        invoiceId: null,
        commodityOrderId: null,
        status: null,
        imgUrl: null,
        orderCompletionTime: null,
        invoicingTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商品订单发票信息列表 */
    getList() {
      this.loading = true;
      listInvoice(this.queryParams).then((response) => {
        this.invoiceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        invoiceId: null,
        commodityOrderId: null,
        status: null,
        imgUrl: null,
        orderCompletionTime: null,
        invoicingTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品订单发票信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInvoice(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品订单发票信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInvoice(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInvoice(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除商品订单发票信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInvoice(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "commodity/invoice/export",
        {
          ...this.queryParams,
        },
        `invoice_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
