<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="订单主键" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入订单主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="退款状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择退款状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退款说明" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入退款说明"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="退款渠道" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择退款渠道"
          clearable
        >
          <el-option
            v-for="dict in dict.type.commodity_pay_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="refundList" border>
      <el-table-column label="订单主键" align="center" prop="id" />
      <el-table-column label="退款状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="退款金额" align="center" prop="amount" />
      <el-table-column label="退款用户主键" align="center" prop="userId" />
      <el-table-column label="退款说明" align="center" prop="subject" />
      <el-table-column
        label="退款返回数据"
        align="center"
        prop="response"
        show-overflow-tooltip
      />
      <el-table-column label="退款渠道" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_pay_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['commodity:refund:reissueRefund']"
            v-if="scope.row.status == 0"
            :loading="loading"
            >重新发起</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listRefund, updateRefund } from "@/api/commodity/refund";

export default {
  name: "Refund",
  dicts: ["sys_yes_no", "commodity_pay_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单退款表格数据
      refundList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        status: null,
        amount: null,
        userId: null,
        subject: null,
        response: null,
        type: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询订单退款列表 */
    getList() {
      this.loading = true;
      listRefund(this.queryParams).then((response) => {
        this.refundList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        status: null,
        amount: null,
        userId: null,
        subject: null,
        response: null,
        type: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      updateRefund(row).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.loading = false;
        this.getList();
      });
    },
  },
};
</script>
