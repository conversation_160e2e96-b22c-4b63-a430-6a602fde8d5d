<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="商家账号" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入商家账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择商品状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.community_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['commodity:info:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="商品名称" align="center" prop="name" />
      <el-table-column label="所属板块" align="center" prop="plateName" />
      <el-table-column label="标题图片" align="center" prop="titleImgUrl">
        <template scope="scope">
          <image-preview
            :src="scope.row.titleImgUrl"
            :width="50"
            :height="50"
          />
        </template>
      </el-table-column>
      <el-table-column label="零售价格" align="center" prop="retailAmount" />
      <el-table-column label="商品运费" align="center" prop="freight" />
      <el-table-column label="运费类型" align="center" prop="freightType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_freight_type"
            :value="scope.row.freightType"
          />
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.community_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="置顶时间" align="center" prop="topUpTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="200px"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleInfo(scope.row)"
            >查看</el-button
          >

          <el-button
            size="mini"
            type="text"
            @click="handleAddExamine(scope.row)"
            v-hasPermi="['commodity:operation:add']"
            v-if="scope.row.status == 0 || scope.row.status == -1"
            >审核</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleTopUpTime(scope.row, 0)"
            v-hasPermi="['commodity:info:topUpTime']"
            v-if="scope.row.topUpTime"
            >取消置顶</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleTopUpTime(scope.row, 1)"
            v-hasPermi="['commodity:info:topUpTime']"
            v-if="!scope.row.topUpTime"
            >置顶</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleShelf(scope.row)"
            v-hasPermi="['commodity:info:edit']"
            v-if="scope.row.status == 1 || scope.row.status == 3"
            >上架</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleOffShel(scope.row)"
            v-hasPermi="['commodity:info:edit']"
            v-if="scope.row.status == 2"
            >下架</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, 0)"
            v-if="scope.row.status != 4"
            v-hasPermi="['commodity:info:remove']"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, 1)"
            v-if="scope.row.status != 4"
            v-hasPermi="['commodity:info:remove']"
            >删除并退保证金</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['commodity:info:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleSync(scope.row)"
            v-if="scope.row.sourceType == 2 && scope.row.status != 4"
            v-hasPermi="['commodity:info:edit']"
            >同步</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="title"
      :visible.sync="openInfo"
      width="80%"
      append-to-body
    >
      <el-divider content-position="left">商家信息</el-divider>
      <el-form label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="店铺名称:">
              {{ enterpriseInfo.name }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法人姓名:">
              {{ enterpriseInfo.legalPersonName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商家类型:">
              {{ enterpriseInfo.type == 1 ? "企业" : "个体户" }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="自营商家:">
              {{ enterpriseInfo.self == 1 ? "是" : "否" }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详细地址:">
              {{ enterpriseInfo.address }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="用户昵称:">
              {{ sysUserInfo.nickName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号码:">
              {{ sysUserInfo.phonenumber }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">商品基本信息</el-divider>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品名称:">
              {{ form.name }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="标题图片:">
              <image-preview :src="form.titleImgUrl" :width="50" :height="50" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="详情图片:">
              <image-preview
                :src="form.detailsImgUrl"
                :width="50"
                :height="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品板块:">
              <span v-if="form.commodityPlate">{{
                form.commodityPlate.name
              }}</span>
              <span v-else>普通商品</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="form.productNature">
            <el-form-item label="产品性质:">
              <dict-tag
                :options="dict.type.commodity_product_nature"
                :value="form.productNature"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="零售价格:">
              {{ form.retailAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品运费:">
              {{ form.freight }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品类型:">
              {{ form.typeName }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-form-item label="商品详情:">
            <div v-html="form.details"></div>
          </el-form-item>
        </el-row>
      </el-form>
      <el-divider content-position="left">商品规格信息</el-divider>
      <el-table v-loading="loading" :data="specificationsList" border>
        <el-table-column label="规格名称" align="center" prop="name" />
        <el-table-column label="库存数量" align="center" prop="stock" />
        <el-table-column label="价格" align="center" prop="price" />
        <el-table-column
          label="供应商零售价格"
          align="center"
          prop="supplierPrice"
        />
        <el-table-column label="供货价格" align="center" prop="supplyAmount" />
        <el-table-column
          label="供货价格浮动比例"
          align="center"
          prop="floatingRatio"
        />
        <el-table-column label="规格图片" align="center" prop="imgUrl">
          <template slot-scope="scope">
            <image-preview :src="scope.row.imgUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
      </el-table>
      <el-divider content-position="left">商品操作信息</el-divider>
      <el-table v-loading="loading" :data="operationList" border>
        <el-table-column label="操作类型" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.community_operation_type"
              :value="scope.row.operationType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作描述"
          align="center"
          prop="operationDescribe"
        />
        <el-table-column label="操作人" align="center" prop="createBy" />
        <el-table-column label="操作时间" align="center" prop="createTime" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openInfo = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核商品管理对话框 -->
    <el-dialog
      title="审核商品"
      :visible.sync="openExamine"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formExamine"
        :model="formExamine"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="是否通过" prop="operationType">
          <el-radio-group v-model="formExamine.operationType">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核描述" prop="operationDescribe">
          <el-input
            v-model="formExamine.operationDescribe"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormExamine">确 定</el-button>
        <el-button @click="openExamine = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改商品管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品名称" prop="name">
              <el-input
                v-model="form.name"
                type="textarea"
                :rows="3"
                placeholder="请输入商品名称"
                :maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品详情" prop="details">
              <el-input
                v-model="form.details"
                type="textarea"
                :rows="3"
                placeholder="请输入内容"
                :maxlength="10000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="所属板块"
              prop="plateId"
              v-if="queryParams.sourceType == 2"
            >
              <el-select v-model="form.plateId" placeholder="请选择所属板块">
                <el-option
                  v-for="dict in plateList"
                  :disabled="dict.id == 1"
                  :key="dict.id"
                  :label="dict.name"
                  :value="parseInt(dict.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属分类" prop="type">
              <el-select v-model="form.type" placeholder="请选择所属分类">
                <el-option
                  v-for="dict in typeList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="parseInt(dict.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="零售价格" prop="retailAmount">
              <el-input-number
                :precision="2"
                :step="0.1"
                :min="0"
                v-model="form.retailAmount"
                placeholder="请输入零售价格"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="运费类型" prop="freightType">
              <el-select
                v-model="form.freightType"
                placeholder="请选择运费类型"
              >
                <el-option
                  v-for="dict in dict.type.commodity_freight_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品运费" prop="freight">
              <el-input-number
                :disabled="form.freightType == 1"
                :precision="2"
                :step="0.1"
                :min="0"
                v-model="form.freight"
                placeholder="请输入商品运费"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题图片" prop="titleImgUrl">
              <image-upload v-model="form.titleImgUrl" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详情图片" prop="detailsImgUrl">
              <image-upload v-model="form.detailsImgUrl" />
            </el-form-item> </el-col
        ></el-row>

        <el-divider content-position="center">商品规格信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAddSxscCommoditySpecifications"
              >添加</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              @click="handleDeleteSxscCommoditySpecifications"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <el-table
          border
          :data="specificationsList"
          @selection-change="handleSxscCommoditySpecificationsSelectionChange"
          ref="sxscCommoditySpecifications"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="规格名称" prop="name" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.name"
                placeholder="请输入规格名称"
                maxlength="50"
                show-word-limit
              />
            </template>
          </el-table-column>
          <el-table-column label="库存数量" prop="stock" align="center">
            <template slot-scope="scope">
              <el-input-number
                :precision="0"
                style="width: 80%"
                :controls="false"
                :step="1"
                :min="0"
                v-model="scope.row.stock"
                placeholder="请输入库存数量"
              />
            </template>
          </el-table-column>
          <el-table-column label="价格" prop="price" align="center">
            <template slot-scope="scope">
              <el-input-number
                :precision="2"
                style="width: 80%"
                :controls="false"
                :step="1"
                :min="0"
                v-model="scope.row.price"
                placeholder="请输入价格"
              />
            </template>
          </el-table-column>
          <el-table-column label="供应商零售价格" prop="price" align="center">
            <template slot-scope="scope">
              <el-input-number
                :precision="2"
                style="width: 80%"
                :controls="false"
                :step="1"
                :min="0"
                :disabled="true"
                v-model="scope.row.supplierPrice"
                placeholder="请输入价格"
              />
            </template>
          </el-table-column>
          <el-table-column label="供货价格" prop="supplyAmount" align="center">
            <template slot-scope="scope">
              <el-input-number
                :precision="2"
                style="width: 80%"
                :controls="false"
                :step="1"
                :min="0"
                v-model="scope.row.supplyAmount"
                placeholder="请输入价格"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="供货价格浮动比例"
            prop="floatingRatio"
            align="center"
          >
            <template slot-scope="scope">
              <el-input-number
                :precision="2"
                style="width: 80%"
                :controls="false"
                :step="0.01"
                :min="0"
                :max="10"
                v-model="scope.row.floatingRatio"
                placeholder="请输入浮动比例"
              />
            </template>
          </el-table-column>
          <el-table-column label="图片" prop="imgUrl" align="center">
            <template slot-scope="scope">
              <image-upload v-model="scope.row.imgUrl" :limit="1" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
  shelf,
  offShel,
  topUpTime,
  cancelTopUpTime,
  dingdongSync,
} from "@/api/commodity/info";
import { listPlate } from "@/api/commodity/plate";
import { listType } from "@/api/commodity/type";
import { listOperation, addOperation } from "@/api/commodity/operation";
export default {
  name: "Info",
  dicts: [
    "commodity_freight_type",
    "community_status",
    "community_operation_type",
    "commodity_product_nature",
    "sys_yes_no",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品管理表格数据
      infoList: [],
      plateList: [],
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openInfo: false,
      openExamine: false,
      formExamine: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        type: null,
        freightType: null,
        status: null,
        plateId: null,
        sourceType: 1,
        createBy: null,
        params: {
          ordinary: null,
        },
      },
      // 表单参数
      form: {},
      enterpriseInfo: {},
      sysUserInfo: {},
      operationList: [],
      specificationsList: [],
      // 表单校验
      rules: {
        titleImgUrl: [
          { required: true, message: "标题图片不能为空", trigger: "blur" },
        ],
        plateId: [
          { required: true, message: "所属板块不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "商品名称不能为空", trigger: "blur" },
        ],
        type: [
          { required: true, message: "所属分类不能为空", trigger: "change" },
        ],
        supplyAmount: [
          { required: true, message: "供货价格不能为空", trigger: "blur" },
        ],
        retailAmount: [
          { required: true, message: "零售价格不能为空", trigger: "blur" },
        ],
        details: [
          { required: true, message: "商品详情不能为空", trigger: "blur" },
        ],
        detailsImgUrl: [
          { required: true, message: "详情图片不能为空", trigger: "blur" },
        ],
        freight: [
          { required: true, message: "商品运费不能为空", trigger: "blur" },
        ],
        freightType: [
          { required: true, message: "运费类型不能为空", trigger: "change" },
        ],
        operationType: [
          {
            required: true,
            message: "操作类型不能为空",
            trigger: "change",
          },
        ],
        operationDescribe: [
          { required: true, message: "操作说明不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    if (this.$route.query.plateId) {
      this.queryParams.plateId = this.$route.query.plateId;
    }
    if (this.$route.query.sourceType) {
      this.queryParams.sourceType = this.$route.query.sourceType;
    }
    this.queryParams.params.benefitRights = this.$route.query.benefitRights;
    this.getList();
  },
  methods: {
    /** 查询商品管理列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      /** 查询商品板块列表 */
      listPlate().then((response) => {
        this.plateList = response.rows;
        this.plateList.push({ id: 0, name: "普通商品" });
      });
      /** 查询商品分类列表 */
      listType().then((response) => {
        this.typeList = response.rows;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        titleImgUrl: null,
        name: null,
        type: null,
        supplyAmount: null,
        retailAmount: null,
        details: null,
        detailsImgUrl: null,
        freight: null,
        freightType: null,
        productNature: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.specificationsList = [];
      this.resetForm("form");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    resetExamine() {
      this.formExamine = {
        id: null,
        commodityId: null,
        operationType: null,
        operationDescribe: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("formExamine");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleInfo(row) {
      const id = row.id || this.ids;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.openInfo = true;
        this.title = "商品信息";
        this.specificationsList = response.data.specifications;
        this.enterpriseInfo = response.data.enterprise || {};
        this.sysUserInfo = response.data.sysUser || {};
      });
      this.operationList = [];
      listOperation({ commodityId: row.id }).then((response) => {
        this.operationList = response.rows;
      });
    },
    /** 审核按钮操作 */
    handleAddExamine(row) {
      this.resetExamine();
      this.formExamine.commodityId = row.id;
      this.openExamine = true;
    },
    /** 置顶按钮操作 */
    handleTopUpTime(row, type) {
      if (type == 1) {
        topUpTime(row.id).then((response) => {
          this.$modal.msgSuccess("操作成功");
          this.getList();
        });
      }
      if (type == 0) {
        cancelTopUpTime(row.id).then((response) => {
          this.$modal.msgSuccess("操作成功");
          this.getList();
        });
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品管理";
      this.form.plateId = this.queryParams.plateId;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.specificationsList = response.data.specifications;
        this.open = true;
        this.title = "修改商品管理";
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.specifications = this.specificationsList;
          if (this.form.id != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row, refundType) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除商品为"' + row.name + '"的数据项？')
        .then(function () {
          return delInfo(ids, refundType);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    submitFormExamine() {
      this.$refs["formExamine"].validate((valid) => {
        if (valid) {
          let data = { ...this.formExamine };
          data.operationType = this.formExamine.operationType == 1 ? 1 : 5;
          addOperation(data).then((response) => {
            this.$modal.msgSuccess("操作成功");
            this.openExamine = false;
            this.getList();
          });
        }
      });
    },
    handleShelf(row) {
      shelf(row.id).then((response) => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      });
    },
    handleOffShel(row) {
      offShel(row.id).then((response) => {
        this.$modal.msgSuccess("操作成功");
        this.getList();
      });
    },
    /** 商品规格添加按钮操作 */
    handleAddSxscCommoditySpecifications() {
      let obj = {};
      obj.imgUrl = "";
      obj.name = "";
      obj.stock = "";
      obj.price = "";
      obj.supplyAmount = "";
      obj.floatingRatio = 0;
      obj.supplierPrice = 0;
      this.specificationsList.push(obj);
    },
    /** 商品规格删除按钮操作 */
    handleDeleteSxscCommoditySpecifications() {
      if (this.checkedSxscCommoditySpecifications.length == 0) {
        this.$modal.msgError("请先选择要删除的商品规格数据");
      } else {
        const specificationsList = this.specificationsList;
        const checkedSxscCommoditySpecifications =
          this.checkedSxscCommoditySpecifications;
        this.specificationsList = specificationsList.filter(function (item) {
          return checkedSxscCommoditySpecifications.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleSxscCommoditySpecificationsSelectionChange(selection) {
      this.checkedSxscCommoditySpecifications = selection.map(
        (item) => item.index
      );
    },
    //供应链商品数据同步
    handleSync(row) {
      dingdongSync(row.id).then((response) => {
        this.$modal.msgSuccess("同步成功");
        this.getList();
      });
    },
  },
};
</script>
