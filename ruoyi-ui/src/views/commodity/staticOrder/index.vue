<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="订单编号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单名称" prop="subject">
        <el-input
          v-model="queryParams.subject"
          placeholder="请输入订单名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="订单金额" prop="totalAmount">
        <el-input
          v-model="queryParams.totalAmount"
          placeholder="请输入订单金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付时间" prop="receiptDate">
        <el-date-picker
          clearable
          v-model="queryParams.receiptDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择支付时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="staticOrderList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="订单主键" align="center" prop="id" />
      <el-table-column label="订单名称" align="center" prop="subject" />
      <el-table-column
        label="商家昵称"
        align="center"
        prop="sellerUser.nickName"
      />
      <el-table-column
        label="商家手机号"
        align="center"
        prop="sellerUser.phonenumber"
      />
      <el-table-column
        label="消费者昵称"
        align="center"
        prop="buyerUser.nickName"
      />
      <el-table-column
        label="消费者手机号"
        align="center"
        prop="buyerUser.phonenumber"
      />
      <el-table-column label="利润比例" align="center" prop="proportion" />
      <el-table-column label="订单金额" align="center" prop="totalAmount" />
      <el-table-column label="状态" align="center" prop="status"
        ><template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_commodity_order_pay_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="receiptDate">
      </el-table-column>
      <el-table-column label="支付类型" align="center" prop="payType"
        ><template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_pay_type"
            :value="scope.row.payType"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listStaticOrder,
  getStaticOrder,
  delStaticOrder,
  addStaticOrder,
  updateStaticOrder,
} from "@/api/commodity/staticOrder";

export default {
  name: "StaticOrder",
  dicts: ["commodity_pay_type", "sxsc_commodity_order_pay_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 静态收款码订单表格数据
      staticOrderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subject: null,
        sellerUserId: null,
        buyerUserId: null,
        totalAmount: null,
        status: null,
        receiptDate: null,
        payType: null,
        failResponse: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询静态收款码订单列表 */
    getList() {
      this.loading = true;
      listStaticOrder(this.queryParams).then((response) => {
        this.staticOrderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        subject: null,
        sellerUserId: null,
        buyerUserId: null,
        proportion: null,
        totalAmount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        status: null,
        receiptDate: null,
        payType: null,
        failResponse: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加静态收款码订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStaticOrder(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改静态收款码订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateStaticOrder(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStaticOrder(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除静态收款码订单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStaticOrder(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "commodity/staticOrder/export",
        {
          ...this.queryParams,
        },
        `staticOrder_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
