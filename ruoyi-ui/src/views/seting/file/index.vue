<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="总览" name="first">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
        >
          <el-form-item label="文件名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入文件名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="文件类型" prop="type">
            <el-select
              v-model="queryParams.type"
              placeholder="请选择文件类型"
              clearable
            >
              <el-option
                v-for="dict in dict.type.sxsc_seting_file_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['seting:file:add']"
              >新增</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="fileList"
          @selection-change="handleSelectionChange"
          border
        >
          <el-table-column label="文件名称" align="center" prop="name" />
          <el-table-column label="文件信息" align="center" prop="url">
            <template slot-scope="scope">
              <image-preview :src="scope.row.url" :width="50" :height="50" />
            </template>
          </el-table-column>
          <el-table-column label="文件类型" align="center" prop="type">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sxsc_seting_file_type"
                :value="scope.row.type"
              />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" />
          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column label="是否删除" align="center" prop="delFlag">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sys_yes_no"
                :value="scope.row.delFlag"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="合资公司文件" name="second"></el-tab-pane>
      <el-tab-pane
        v-for="(item, index) in cardFileList"
        :key="index"
        :label="item.name"
        :name="index"
      >
        <div style="width: 100%; text-align: center; padding: 30px">
          <image-preview :src="item.url" width="70%" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改附件信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文件名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入文件名称" />
        </el-form-item>
        <el-form-item label="文件信息" prop="url">
          <image-upload v-model="form.url" />
        </el-form-item>
        <el-form-item label="文件类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择文件类型">
            <el-option
              v-for="dict in dict.type.sxsc_seting_file_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFile,
  getFile,
  delFile,
  addFile,
  updateFile,
} from "@/api/seting/file";

export default {
  name: "File",
  dicts: ["sxsc_seting_file_type", "sys_yes_no"],
  data() {
    return {
      activeName: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 附件信息表格数据
      fileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        url: null,
        type: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "文件名称不能为空", trigger: "blur" },
        ],
        url: [{ required: true, message: "文件信息不能为空", trigger: "blur" }],
        type: [
          { required: true, message: "文件类型不能为空", trigger: "change" },
        ],
      },
      cardFileList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询附件信息列表 */
    getList() {
      this.loading = true;
      listFile(this.queryParams).then((response) => {
        this.fileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      listFile({ delFlag: 0, type: 2 }).then((response) => {
        this.cardFileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        url: null,
        type: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加附件信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getFile(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改附件信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateFile(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFile(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除附件信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delFile(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "sxsc/file/export",
        {
          ...this.queryParams,
        },
        `file_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
