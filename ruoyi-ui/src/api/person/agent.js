import request from "@/utils/request";

// 查询代理信息列表
export function listAgent(query) {
  return request({
    url: "/person/agent/list",
    method: "get",
    params: query,
  });
}

// 查询代理信息详细
export function getAgent(id) {
  return request({
    url: "/person/agent/" + id,
    method: "get",
  });
}

// 查询代理统计信息
export function getAgentStatistics(query) {
  return request({
    url: "/person/agent/statistics",
    method: "get",
    params: query,
  });
}

// 新增代理信息
export function addAgent(data) {
  return request({
    url: "/person/agent",
    method: "post",
    data: data,
  });
}

// 修改代理信息
export function updateAgent(data) {
  return request({
    url: "/person/agent",
    method: "put",
    data: data,
  });
}

// 删除代理信息
export function delAgent(id) {
  return request({
    url: "/person/agent/" + id,
    method: "delete",
  });
}

// 获取地址信息
export function district() {
  return request({
    url: "/person/agent/district",
    method: "get",
  });
}
