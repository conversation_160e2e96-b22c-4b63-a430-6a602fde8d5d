import request from '@/utils/request'

// 查询佣金提现额度列表
export function listQuota(query) {
  return request({
    url: '/person/quota/list',
    method: 'get',
    params: query
  })
}

// 查询佣金提现额度详细
export function getQuota(id) {
  return request({
    url: '/person/quota/' + id,
    method: 'get'
  })
}

// 新增佣金提现额度
export function addQuota(data) {
  return request({
    url: '/person/quota',
    method: 'post',
    data: data
  })
}

// 修改佣金提现额度
export function updateQuota(data) {
  return request({
    url: '/person/quota',
    method: 'put',
    data: data
  })
}

// 删除佣金提现额度
export function delQuota(id) {
  return request({
    url: '/person/quota/' + id,
    method: 'delete'
  })
}
