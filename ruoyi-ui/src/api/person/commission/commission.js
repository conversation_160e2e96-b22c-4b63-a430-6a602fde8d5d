import request from "@/utils/request";

// 查询佣金信息列表
export function listCommission(query) {
  return request({
    url: "/person/commission/list",
    method: "get",
    params: query,
  });
}

// 查询佣金信息详细
export function getCommission(id) {
  return request({
    url: "/person/commission/" + id,
    method: "get",
  });
}

// 新增佣金信息
export function addCommission(data) {
  return request({
    url: "/person/commission",
    method: "post",
    data: data,
  });
}

// 修改佣金信息
export function updateCommission(data) {
  return request({
    url: "/person/commission",
    method: "put",
    data: data,
  });
}

// 删除佣金信息
export function delCommission(id) {
  return request({
    url: "/person/commission/" + id,
    method: "delete",
  });
}
// 查询佣金信息统计
export function statisticsCommission(query) {
  return request({
    url: "/person/commission/statistics",
    method: "get",
    params: query,
  });
}
