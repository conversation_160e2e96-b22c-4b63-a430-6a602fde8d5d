import request from '@/utils/request'

// 查询首页轮播图列表
export function listCarousel(query) {
  return request({
    url: '/operations/carousel/list',
    method: 'get',
    params: query
  })
}

// 查询首页轮播图详细
export function getCarousel(id) {
  return request({
    url: '/operations/carousel/' + id,
    method: 'get'
  })
}

// 新增首页轮播图
export function addCarousel(data) {
  return request({
    url: '/operations/carousel',
    method: 'post',
    data: data
  })
}

// 修改首页轮播图
export function updateCarousel(data) {
  return request({
    url: '/operations/carousel',
    method: 'put',
    data: data
  })
}

// 删除首页轮播图
export function delCarousel(id) {
  return request({
    url: '/operations/carousel/' + id,
    method: 'delete'
  })
}
