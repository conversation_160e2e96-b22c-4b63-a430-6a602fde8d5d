import request from '@/utils/request'

// 查询个人工具信息列表
export function listTools(query) {
  return request({
    url: '/operations/tools/list',
    method: 'get',
    params: query
  })
}

// 查询个人工具信息详细
export function getTools(id) {
  return request({
    url: '/operations/tools/' + id,
    method: 'get'
  })
}

// 新增个人工具信息
export function addTools(data) {
  return request({
    url: '/operations/tools',
    method: 'post',
    data: data
  })
}

// 修改个人工具信息
export function updateTools(data) {
  return request({
    url: '/operations/tools',
    method: 'put',
    data: data
  })
}

// 删除个人工具信息
export function delTools(id) {
  return request({
    url: '/operations/tools/' + id,
    method: 'delete'
  })
}
