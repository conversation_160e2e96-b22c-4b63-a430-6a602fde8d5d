import request from '@/utils/request'

// 查询金刚区列表
export function listDistrict(query) {
  return request({
    url: '/operations/district/list',
    method: 'get',
    params: query
  })
}

// 查询金刚区详细
export function getDistrict(id) {
  return request({
    url: '/operations/district/' + id,
    method: 'get'
  })
}

// 新增金刚区
export function addDistrict(data) {
  return request({
    url: '/operations/district',
    method: 'post',
    data: data
  })
}

// 修改金刚区
export function updateDistrict(data) {
  return request({
    url: '/operations/district',
    method: 'put',
    data: data
  })
}

// 删除金刚区
export function delDistrict(id) {
  return request({
    url: '/operations/district/' + id,
    method: 'delete'
  })
}
