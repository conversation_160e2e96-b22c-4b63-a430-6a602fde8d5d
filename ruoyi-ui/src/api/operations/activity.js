import request from '@/utils/request'

// 查询首页活动区列表
export function listActivity(query) {
  return request({
    url: '/operations/activity/list',
    method: 'get',
    params: query
  })
}

// 查询首页活动区详细
export function getActivity(id) {
  return request({
    url: '/operations/activity/' + id,
    method: 'get'
  })
}

// 新增首页活动区
export function addActivity(data) {
  return request({
    url: '/operations/activity',
    method: 'post',
    data: data
  })
}

// 修改首页活动区
export function updateActivity(data) {
  return request({
    url: '/operations/activity',
    method: 'put',
    data: data
  })
}

// 删除首页活动区
export function delActivity(id) {
  return request({
    url: '/operations/activity/' + id,
    method: 'delete'
  })
}
