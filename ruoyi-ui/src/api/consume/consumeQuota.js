import request from '@/utils/request'

// 查询额度明细列表
export function listDetail(query) {
  return request({
    url: '/consumeQuota/detail/list',
    method: 'get',
    params: query
  })
}

// 查询额度明细详细
export function getDetail(id) {
  return request({
    url: '/consumeQuota/detail/' + id,
    method: 'get'
  })
}

// 新增额度明细
export function addDetail(data) {
  return request({
    url: '/consumeQuota/detail',
    method: 'post',
    data: data
  })
}

// 修改额度明细
export function updateDetail(data) {
  return request({
    url: '/consumeQuota/detail',
    method: 'put',
    data: data
  })
}

// 删除额度明细
export function delDetail(id) {
  return request({
    url: '/consumeQuota/detail/' + id,
    method: 'delete'
  })
}
