import request from '@/utils/request'

// 查询优惠券抵押父级获得奖励明细列表
export function listParentTeward(query) {
  return request({
    url: '/consume/parentTeward/list',
    method: 'get',
    params: query
  })
}

// 查询优惠券抵押父级获得奖励明细详细
export function getParentTeward(id) {
  return request({
    url: '/consume/parentTeward/' + id,
    method: 'get'
  })
}

// 新增优惠券抵押父级获得奖励明细
export function addParentTeward(data) {
  return request({
    url: '/consume/parentTeward',
    method: 'post',
    data: data
  })
}

// 修改优惠券抵押父级获得奖励明细
export function updateParentTeward(data) {
  return request({
    url: '/consume/parentTeward',
    method: 'put',
    data: data
  })
}

// 删除优惠券抵押父级获得奖励明细
export function delParentTeward(id) {
  return request({
    url: '/consume/parentTeward/' + id,
    method: 'delete'
  })
}
