import request from "@/utils/request";

// 查询优惠券合成列表
export function listCompound(query) {
  return request({
    url: "/consume/compound/list",
    method: "get",
    params: query,
  });
}

// 查询优惠券合成详细
export function getCompound(id) {
  return request({
    url: "/consume/compound/" + id,
    method: "get",
  });
}

// 新增优惠券合成
export function addCompound(data) {
  return request({
    url: "/consume/compound",
    method: "post",
    data: data,
  });
}

// 修改优惠券合成
export function updateCompound(data) {
  return request({
    url: "/consume/compound",
    method: "put",
    data: data,
  });
}

// 删除优惠券合成
export function delCompound(id) {
  return request({
    url: "/consume/compound/" + id,
    method: "delete",
  });
}
