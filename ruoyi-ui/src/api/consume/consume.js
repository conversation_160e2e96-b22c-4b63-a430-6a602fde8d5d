import request from "@/utils/request";

// 查询优惠券列表
export function listConsume(query) {
  return request({
    url: "/consume/list",
    method: "get",
    params: query,
  });
}

// 查询优惠券详细
export function getConsume(consumeNumber) {
  return request({
    url: "/consume/" + consumeNumber,
    method: "get",
  });
}

// 统计优惠券信息
export function statisticsConsume() {
  return request({
    url: "/consume/statistics",
    method: "get",
  });
}


// 修改优惠券
export function updateConsume(data) {
  return request({
    url: "/consume",
    method: "put",
    data: data,
  });
}

// 删除优惠券
export function delConsume(consumeNumber) {
  return request({
    url: "/consume/" + consumeNumber,
    method: "delete",
  });
}
