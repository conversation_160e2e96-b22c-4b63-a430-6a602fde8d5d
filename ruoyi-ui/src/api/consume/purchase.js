import request from '@/utils/request'

// 查询优惠券预购订单列表
export function listPurchase(query) {
  return request({
    url: '/consume/purchase/list',
    method: 'get',
    params: query
  })
}

// 查询优惠券预购订单详细
export function getPurchase(id) {
  return request({
    url: '/consume/purchase/' + id,
    method: 'get'
  })
}

// 新增优惠券预购订单
export function addPurchase(data) {
  return request({
    url: '/consume/purchase',
    method: 'post',
    data: data
  })
}

// 修改优惠券预购订单
export function updatePurchase(data) {
  return request({
    url: '/consume/purchase',
    method: 'put',
    data: data
  })
}

// 删除优惠券预购订单
export function delPurchase(id) {
  return request({
    url: '/consume/purchase/' + id,
    method: 'delete'
  })
}
