import request from "@/utils/request";

// 查询优惠券拆分列表
export function listSplit(query) {
  return request({
    url: "/consume/split/list",
    method: "get",
    params: query,
  });
}

// 查询优惠券拆分详细
export function getSplit(id) {
  return request({
    url: "/consume/split/" + id,
    method: "get",
  });
}

// 新增优惠券拆分
export function addSplit(data) {
  return request({
    url: "/consume/split",
    method: "post",
    data: data,
  });
}

// 修改优惠券拆分
export function updateSplit(data) {
  return request({
    url: "/consume/split",
    method: "put",
    data: data,
  });
}

// 删除优惠券拆分
export function delSplit(id) {
  return request({
    url: "/consume/split/" + id,
    method: "delete",
  });
}
