import request from "@/utils/request";

// 查询优惠券回购信息列表
export function listBack(query) {
  return request({
    url: "/consume/back/list",
    method: "get",
    params: query,
  });
}

// 查询优惠券回购信息详细
export function getBack(id) {
  return request({
    url: "/consume/back/" + id,
    method: "get",
  });
}

// 新增优惠券回购信息
export function addBack(data) {
  return request({
    url: "/consume/back",
    method: "post",
    data: data,
  });
}

// 修改优惠券回购信息
export function updateBack(data) {
  return request({
    url: "/consume/back",
    method: "put",
    data: data,
  });
}

// 删除优惠券回购信息
export function delBack(id) {
  return request({
    url: "/consume/back/" + id,
    method: "delete",
  });
}
