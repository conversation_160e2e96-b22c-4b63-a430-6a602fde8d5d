import request from '@/utils/request'

// 查询系统赠送优惠券列表
export function listSystemGive(query) {
  return request({
    url: '/consume/systemGive/list',
    method: 'get',
    params: query
  })
}

// 查询系统赠送优惠券详细
export function getSystemGive(id) {
  return request({
    url: '/consume/systemGive/' + id,
    method: 'get'
  })
}

// 新增系统赠送优惠券
export function addSystemGive(data) {
  return request({
    url: '/consume/systemGive',
    method: 'post',
    data: data
  })
}

// 修改系统赠送优惠券
export function updateSystemGive(data) {
  return request({
    url: '/consume/systemGive',
    method: 'put',
    data: data
  })
}

// 删除系统赠送优惠券
export function delSystemGive(id) {
  return request({
    url: '/consume/systemGive/' + id,
    method: 'delete'
  })
}
