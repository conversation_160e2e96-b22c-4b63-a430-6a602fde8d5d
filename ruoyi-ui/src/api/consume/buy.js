import request from "@/utils/request";

// 查询优惠券抵押明细列表
export function listBuy(query) {
  return request({
    url: "/consume/buy/list",
    method: "get",
    params: query,
  });
}

// 查询优惠券抵押明细详细
export function getBuy(id) {
  return request({
    url: "/consume/buy/" + id,
    method: "get",
  });
}

// 修改优惠券抵押明细
export function updateBuy(data) {
  return request({
    url: "/consume/buy",
    method: "put",
    data: data,
  });
}
