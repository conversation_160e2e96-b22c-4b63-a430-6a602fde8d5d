import request from "@/utils/request";

// 查询微信转账信息列表
export function listWeChatTransfer(query) {
  return request({
    url: "/wechat/payment/transfer/list",
    method: "get",
    params: query,
  });
}

// 查询微信转账信息详细
export function getWeChatTransfer(id) {
  return request({
    url: "/wechat/payment/transfer/" + id,
    method: "get",
  });
}

// 新增微信转账信息
export function addWeChatTransfer(data) {
  return request({
    url: "/wechat/payment/transfer",
    method: "post",
    data: data,
  });
}

// 修改微信转账信息
export function updateWeChatTransfer(data) {
  return request({
    url: "/wechat/payment/transfer",
    method: "put",
    data: data,
  });
}

// 删除微信转账信息
export function delWeChatTransfer(id) {
  return request({
    url: "/wechat/payment/transfer/" + id,
    method: "delete",
  });
}

// 重新发起转账
export function reissueWeChatTransfer(id) {
  return request({
    url: "/wechat/payment/transfer/reissue/" + id,
    method: "put",
  });
}

// 查询转账状态
export function queryWeChatTransferStatus(outBatchNo) {
  return request({
    url: "/wechat/payment/transfer/query/" + outBatchNo,
    method: "get",
  });
}

// 获取微信转账统计数据
export function getWeChatTransferAmount(query) {
  return request({
    url: "/wechat/payment/transfer/amount",
    method: "get",
    params: query,
  });
}
