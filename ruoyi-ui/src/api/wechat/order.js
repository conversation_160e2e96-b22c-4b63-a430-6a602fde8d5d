import request from "@/utils/request";

// 查询微信支付信息列表
export function listWeChatOrder(query) {
  return request({
    url: "/wechat/payment/order/list",
    method: "get",
    params: query,
  });
}

// 查询微信支付信息详细
export function getWeChatOrder(id) {
  return request({
    url: "/wechat/payment/order/" + id,
    method: "get",
  });
}

// 新增微信支付信息
export function addWeChatOrder(data) {
  return request({
    url: "/wechat/payment/order",
    method: "post",
    data: data,
  });
}

// 修改微信支付信息
export function updateWeChatOrder(data) {
  return request({
    url: "/wechat/payment/order",
    method: "put",
    data: data,
  });
}

// 删除微信支付信息
export function delWeChatOrder(id) {
  return request({
    url: "/wechat/payment/order/" + id,
    method: "delete",
  });
}

// 查询订单状态
export function queryWeChatOrderStatus(outTradeNo) {
  return request({
    url: "/wechat/payment/order/query/" + outTradeNo,
    method: "get",
  });
}

// 关闭订单
export function closeWeChatOrder(outTradeNo) {
  return request({
    url: "/wechat/payment/order/close/" + outTradeNo,
    method: "post",
  });
}

// 手动回调
export function weChatOffline(batchId) {
  return request({
    url: "/wechat/payment/order/wechat-trends/offline/" + batchId,
    method: "post",
  });
}

// 获取微信支付统计数据
export function weChatPayAmount(query) {
  return request({
    url: "/wechat/payment/order/amount",
    method: "get",
    params: query,
  });
}
