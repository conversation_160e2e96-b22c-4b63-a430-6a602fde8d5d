import request from '@/utils/request'

// 查询静态收款码订单列表
export function listStaticOrder(query) {
  return request({
    url: '/commodity/staticOrder/list',
    method: 'get',
    params: query
  })
}

// 查询静态收款码订单详细
export function getStaticOrder(id) {
  return request({
    url: '/commodity/staticOrder/' + id,
    method: 'get'
  })
}

// 新增静态收款码订单
export function addStaticOrder(data) {
  return request({
    url: '/commodity/staticOrder',
    method: 'post',
    data: data
  })
}

// 修改静态收款码订单
export function updateStaticOrder(data) {
  return request({
    url: '/commodity/staticOrder',
    method: 'put',
    data: data
  })
}

// 删除静态收款码订单
export function delStaticOrder(id) {
  return request({
    url: '/commodity/staticOrder/' + id,
    method: 'delete'
  })
}
