import request from '@/utils/request'

// 查询商品操作记录列表
export function listOperation(query) {
  return request({
    url: '/commodity/operation/list',
    method: 'get',
    params: query
  })
}

// 查询商品操作记录详细
export function getOperation(id) {
  return request({
    url: '/commodity/operation/' + id,
    method: 'get'
  })
}

// 新增商品操作记录
export function addOperation(data) {
  return request({
    url: '/commodity/operation',
    method: 'post',
    data: data
  })
}

// 修改商品操作记录
export function updateOperation(data) {
  return request({
    url: '/commodity/operation',
    method: 'put',
    data: data
  })
}

// 删除商品操作记录
export function delOperation(id) {
  return request({
    url: '/commodity/operation/' + id,
    method: 'delete'
  })
}
