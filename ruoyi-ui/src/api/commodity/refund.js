import request from "@/utils/request";

// 查询订单退款列表
export function listRefund(query) {
  return request({
    url: "/commodity/refund/list",
    method: "get",
    params: query,
  });
}

// 查询订单退款详细
export function getRefund(id) {
  return request({
    url: "/commodity/refund/" + id,
    method: "get",
  });
}

// 重新发起退款
export function updateRefund(data) {
  return request({
    url: "/commodity/refund/" + data.id,
    method: "put",
    data: data,
  });
}

// 管理后台发起退款
export function manageRefund(data) {
  return request({
    url: "/commodity/refund/manage/" + data.id,
    method: "put",
    data: data,
  });
}

// 管理后台发起退款
export function manageePointsRefund(data) {
  return request({
    url: "/commodity/refund/manage/points/" + data.id,
    method: "put",
    data: data,
  });
}
// 管理后台发起退款
export function manageeEnterprisefund(data) {
  return request({
    url: "/commodity/refund/manage/enterprise/" + data.id,
    method: "put",
    data: data,
  });
}