import request from '@/utils/request'

// 查询商品板块列表
export function listPlate(query) {
  return request({
    url: '/commodity/plate/list',
    method: 'get',
    params: query
  })
}

// 查询商品板块详细
export function getPlate(id) {
  return request({
    url: '/commodity/plate/' + id,
    method: 'get'
  })
}

// 新增商品板块
export function addPlate(data) {
  return request({
    url: '/commodity/plate',
    method: 'post',
    data: data
  })
}

// 修改商品板块
export function updatePlate(data) {
  return request({
    url: '/commodity/plate',
    method: 'put',
    data: data
  })
}

// 删除商品板块
export function delPlate(id) {
  return request({
    url: '/commodity/plate/' + id,
    method: 'delete'
  })
}
