import request from "@/utils/request";

// 查询账单信息列表
export function listIntegral(query) {
  return request({
    url: "/bill/integral/list",
    method: "get",
    params: query,
  });
}

// 查询账单信息详细
export function getIntegral(id) {
  return request({
    url: "/bill/integral/" + id,
    method: "get",
  });
}

// 新增账单信息
export function addIntegral(data) {
  return request({
    url: "/bill/integral",
    method: "post",
    data: data,
  });
}

// 修改账单信息
export function updateIntegral(data) {
  return request({
    url: "/bill/integral",
    method: "put",
    data: data,
  });
}

// 删除账单信息
export function delIntegral(id) {
  return request({
    url: "/bill/integral/" + id,
    method: "delete",
  });
}

// 后台批量增加或减少积分
export function htRechargeBatch(data, type) {
  return request({
    url: "/bill/integral/ht-recharge/batch/" + type,
    method: "post",
    data: data,
  });
}
